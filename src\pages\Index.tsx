
import { useRef } from 'react';
import LuxuryHeader from '@/components/luxury/LuxuryHeader';
import LuxuryHero from '@/components/luxury/LuxuryHero';
import ExclusiveFeatures from '@/components/luxury/ExclusiveFeatures';
import VIPPrograms from '@/components/luxury/VIPPrograms';
import LuxuryTestimonials from '@/components/luxury/LuxuryTestimonials';
import EliteAbout from '@/components/luxury/EliteAbout';
import PremiumContact from '@/components/luxury/PremiumContact';
import LuxuryFooter from '@/components/luxury/LuxuryFooter';

const Index = () => {
  // Create refs for scroll functionality
  const aboutRef = useRef<HTMLDivElement>(null);
  const programsRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);
  
  // Scroll functions
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <LuxuryHeader
        onAboutClick={() => scrollToSection(aboutRef)}
        onProgramsClick={() => scrollToSection(programsRef)}
        onContactClick={() => scrollToSection(contactRef)}
        showVIPIndicator={true}
      />
      <main className="flex-1">
        <LuxuryHero
          title="Elite Promise Academy"
          subtitle="Where luxury meets learning in an exclusive educational experience designed for extraordinary children"
          ctaText="Enter VIP Portal"
          backgroundAnimation={true}
        />
        <div ref={aboutRef}>
          <EliteAbout />
        </div>
        <ExclusiveFeatures layout="grid" showBadges={true} />
        <div ref={programsRef}>
          <VIPPrograms />
        </div>
        <LuxuryTestimonials />
        <div ref={contactRef}>
          <PremiumContact />
        </div>
      </main>
      <LuxuryFooter />
    </div>
  );
};

export default Index;
