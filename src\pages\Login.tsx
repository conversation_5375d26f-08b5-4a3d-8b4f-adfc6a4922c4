
import { LuxuryHeader } from '@/components/luxury';
import LoginForm from '@/components/auth/LoginForm';
import { Crown, Star } from 'lucide-react';

const Login = () => {
  return (
    <div className="min-h-screen flex flex-col relative overflow-hidden">
      {/* Luxury Gold-Purple-Red Background */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(135deg, #FFD700 0%, #663399 50%, #DC143C 100%)'
        }}
      />

      {/* Subtle Background Elements */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-luxury-gold/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-luxury-red/10 rounded-full blur-2xl" />
      <Crown className="absolute top-20 right-20 h-12 w-12 text-luxury-gold/20" />
      <Star className="absolute bottom-32 left-16 h-8 w-8 text-luxury-red/30" />

      <LuxuryHeader showVIPIndicator={false} />
      <main className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <LoginForm />
      </main>
    </div>
  );
};

export default Login;
